import { Component, OnInit, signal, computed, resource, effect } from '@angular/core';
import { AgencyService } from 'src/app/shared/services/agency.service';
import { StorageService } from './../../../shared/services/storage.service';
import { ToastrService } from 'ngx-toastr';
import { Validators, FormControl, FormGroup, AbstractControl, ValidationErrors } from '@angular/forms';
import { FileService } from '../../../shared/services/file.service';
import { HttpResponse } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { CompressImageService } from 'src/app/shared/utilities/compress-image';
import { take } from 'rxjs/operators';
import { lastValueFrom } from 'rxjs';

@Component({
    selector: 'app-config-account',
    templateUrl: './config-account.component.html',
    styleUrls: ['./config-account.component.css'],
    standalone: false
})

export class ConfigAccountComponent implements OnInit {

    // Angular Signals for reactive state
    agencyLogo = signal<File | null>(null);
    isEditingCompanyName = signal<boolean>(false);
    isEditingPhone = signal<boolean>(false);
    isEditingAddress = signal<boolean>(false);
    isEditingSMTP = signal<boolean>(false);
    phoneValidationError = signal<string | null>(null);

    // Loading states for save operations
    savingCompanyName = signal<boolean>(false);
    savingPhone = signal<boolean>(false);
    savingAddress = signal<boolean>(false);
    savingSMTP = signal<boolean>(false);

    // Agency ID as a signal for resource dependency
    agencyId = computed(() => this.storageService.decrypt(localStorage.getItem('agentId')));

    // Agency data resource using Angular resource API
    agencyResource = resource({
        request: this.agencyId,
        loader: ({ request: agencyId }) => {
            if (!agencyId) return Promise.reject(new Error('No agency ID found'));
            return lastValueFrom(this.agencyService.getAgencyById(agencyId));
        },
    });

    // SMTP configuration resource
    smtpConfigResource = resource({
        request: this.agencyId,
        loader: ({ request: agencyId }) => {
            if (!agencyId) return Promise.reject(new Error('No agency ID found'));
            if (!environment.isCarRental || environment.appName === 'Karlink') return Promise.resolve(null);
            return lastValueFrom(this.agencyService.getConfigurationByAgencyId(agencyId));
        },
    });

    // Convenient getters for resource states
    agency = this.agencyResource.value;
    smtpConfig = this.smtpConfigResource.value;
    loading = computed(() => this.agencyResource.isLoading() || this.smtpConfigResource.isLoading());
    error = computed(() => this.agencyResource.error() || this.smtpConfigResource.error());

    // Main form group with nested FormGroups for logical organization
    agencyForm = new FormGroup({
        // Core agency identification
        agencyId: new FormControl(0),
        serviceId: new FormControl(0),

        // Company Profile group
        companyProfile: new FormGroup({
            name: new FormControl('', [Validators.required]),
            logo: new FormControl(''),
            baseCurrency: new FormControl('')
        }),

        // Contact Information group
        contactInfo: new FormGroup({
            email: new FormControl(''),
            telephone: new FormControl('', [Validators.required]),
            billingEmail: new FormControl('')
        }),

        // Address group
        address: new FormGroup({
            firstLine: new FormControl('', [Validators.required]),
            secondLine: new FormControl(''),
            town: new FormControl('', [Validators.required]),
            postcode: new FormControl(''), // Optional - not all countries use postal codes
            country: new FormControl('', [Validators.required]),
            county: new FormControl('')
        })
    });

    // SMTP form controls (mapped to email config API structure)
    smtpForm = new FormGroup({
        fromEmail: new FormControl('', [Validators.required, Validators.email]),
        fromName: new FormControl('', [Validators.required]),
        replyToEmail: new FormControl('', [Validators.email]),
        smtpHost: new FormControl('', [Validators.required]),
        smtpPort: new FormControl('', [Validators.required, Validators.pattern('^[0-9]+$')]),
        smtpUsername: new FormControl('', [Validators.required, Validators.email]),
        smtpPassword: new FormControl('', [Validators.required]),
        smtpStarttlsEnable: new FormControl(true),
        smtpStarttlsRequired: new FormControl(true),
        supportEmail: new FormControl('', [Validators.email]),
        websiteUrl: new FormControl('', [this.urlValidator]),
        notes: new FormControl('')
    });

    // Convenience getters for nested form groups
    get companyProfileForm() { return this.agencyForm.get('companyProfile') as FormGroup; }
    get contactInfoForm() { return this.agencyForm.get('contactInfo') as FormGroup; }
    get addressForm() { return this.agencyForm.get('address') as FormGroup; }

    // Computed values
    profilePicUrl = computed(() => {
        const id = this.agencyId();
        return id ? `${environment.API_url}${environment.coreService}/tina/worklink/agency/${id}/profile.png` : '';
    });
    baseUrl = computed(() => environment.baseUrl);

    constructor(
        private agencyService: AgencyService,
        private fileService: FileService,
        private toast: ToastrService,
        private storageService: StorageService,
        private compressImage: CompressImageService
    ) {
        // Handle resource errors using effect
        effect(() => {
            const resourceError = this.error();
            if (resourceError) {
                this.handleResourceError(resourceError);
            }
        });

        // Auto-populate forms when agency data loads
        effect(() => {
            const agencyData = this.agency();
            if (agencyData) {
                this.populateForms(agencyData);
            }
        });

        // Auto-populate SMTP form when email config loads
        effect(() => {
            const smtpData = this.smtpConfig();
            if (smtpData) {
                this.populateSMTPForm(smtpData);
            }
        });
    }

    ngOnInit(): void {
    }

    private handleResourceError(err: any) {
        if (err.status == 0) {
            this.toast.error('Network Connection Failure');
        } else if (err.error?.message) {
            this.toast.error(err.error.message);
        } else if (err.error?.error?.message) {
            this.toast.error(err.error.error.message);
        } else if (err.status == 500) {
            this.toast.error('Internal Server Error');
        } else {
            this.toast.error('An error occurred while loading agency data');
        }
    }

    private populateForms(data: any) {
        const agencyId = this.agencyId();

        // Populate nested FormGroups
        this.agencyForm.patchValue({
            agencyId: agencyId,
            serviceId: data.serviceId || 0,
            companyProfile: {
                name: data.name || '',
                logo: data.logo || '',
                baseCurrency: data.baseCurrency || ''
            },
            contactInfo: {
                email: data.email || '',
                telephone: data.telephone || '',
                billingEmail: data.billingEmail || ''
            },
            address: {
                firstLine: data.addressFirstLine || '',
                secondLine: data.addressSecondLine || '',
                town: data.town || '',
                postcode: data.postalCode || '',
                country: data.country || '',
                county: data.county || ''
            }
        });
    }

    private populateSMTPForm(data: any) {
        // Populate SMTP form with email config data
        this.smtpForm.patchValue({
            fromEmail: data.fromEmail || '',
            fromName: data.fromName || '',
            replyToEmail: data.replyToEmail || '',
            smtpHost: data.smtpHost || '',
            smtpPort: data.smtpPort || '',
            smtpUsername: data.smtpUsername || '',
            smtpPassword: data.smtpPassword || '',
            smtpStarttlsEnable: data.smtpStarttlsEnable ?? true,
            smtpStarttlsRequired: data.smtpStarttlsRequired ?? true,
            supportEmail: data.supportEmail || '',
            websiteUrl: data.websiteUrl || '',
            notes: data.notes || ''
        });
    }

    refreshAgencyData() {
        this.agencyResource.reload();
        if (this.isSMTPConfigurationAllowed()) {
            this.smtpConfigResource.reload();
        }
    }

    onLogoChange(event: Event) {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];
        if (!file) return;

        console.log(`Image size before compressed: ${file.size} bytes.`);
        this.compressImage.compress(file)
            .pipe(take(1))
            .subscribe(compressedImage => {
                console.log(`Image size after compressed: ${compressedImage.size} bytes.`);
                this.agencyLogo.set(compressedImage);
            });
    }

    uploadLogo() {
        const logo = this.agencyLogo();
        const agencyId = this.agencyId();

        if (!logo || !agencyId) {
            this.toast.warning('Please select a logo first');
            return;
        }

        this.fileService.agencyUploadLogo(logo, agencyId).subscribe({
            next: (event) => {
                if (event instanceof HttpResponse) {
                    this.toast.success('Logo uploaded successfully');
                    this.agencyLogo.set(null);
                    // Reset file input
                    const fileInput = document.getElementById('logoInput') as HTMLInputElement;
                    if (fileInput) fileInput.value = '';
                    this.refreshAgencyData();
                }
            },
            error: () => {
                this.toast.error('Failed to upload logo');
            }
        });
    }

    toggleCompanyNameEdit() {
        this.isEditingCompanyName.set(!this.isEditingCompanyName());
    }

    saveCompanyName() {
        const companyNameControl = this.companyProfileForm.get('name') as FormControl;
        if (companyNameControl.valid) {
            const newName = companyNameControl.value;

            if (!newName) return;

            // Set loading state
            this.savingCompanyName.set(true);

            // Update the nested FormGroup with the new name
            this.companyProfileForm.patchValue({ name: newName });

            // Get the complete form data for API call
            const agencyData = this.getAgencyFormData();

            this.agencyService.updateAgency(agencyData).subscribe({
                next: () => {
                    this.toast.success('Company name updated successfully');
                    this.isEditingCompanyName.set(false);

                    // Refetch agency data to ensure UI has latest values
                    this.refreshAgencyData();
                },
                error: (err) => {
                    this.toast.error('Failed to update company name');
                    console.error('Error updating company name:', err);
                },
                complete: () => {
                    this.savingCompanyName.set(false);
                }
            });
        } else {
            this.toast.warning('Please enter a valid company name');
        }
    }

    cancelCompanyNameEdit() {
        const currentAgency = this.agency();
        this.companyProfileForm.patchValue({ name: currentAgency?.name || '' });
        this.isEditingCompanyName.set(false);
    }

    togglePhoneEdit() {
        this.isEditingPhone.set(!this.isEditingPhone());
    }

    onPhoneValidationChange(validationState: any): void {
        this.phoneValidationError.set(validationState.errorMessage || null);
    }

    savePhone() {
        const phoneControl = this.contactInfoForm.get('telephone') as FormControl;
        if (phoneControl.valid && !this.phoneValidationError()) {
            const newPhone = phoneControl.value;

            if (!newPhone) return;

            // Set loading state
            this.savingPhone.set(true);

            // Update the nested FormGroup with the new phone
            this.contactInfoForm.patchValue({ telephone: newPhone });

            // Get the complete form data for API call
            const agencyData = this.getAgencyFormData();

            this.agencyService.updateAgency(agencyData).subscribe({
                next: () => {
                    this.toast.success('Phone number updated successfully');
                    this.isEditingPhone.set(false);

                    // Refetch agency data to ensure UI has latest values
                    this.refreshAgencyData();
                },
                error: (err) => {
                    this.toast.error('Failed to update phone number');
                    console.error('Error updating phone number:', err);
                },
                complete: () => {
                    this.savingPhone.set(false);
                }
            });
        } else {
            this.toast.warning('Please enter a valid phone number');
        }
    }

    cancelPhoneEdit() {
        const currentAgency = this.agency();
        this.contactInfoForm.patchValue({ telephone: currentAgency?.telephone || '' });
        this.isEditingPhone.set(false);
        this.phoneValidationError.set(null);
    }

    // Address management methods
    toggleAddressEdit() {
        this.isEditingAddress.set(!this.isEditingAddress());
    }

    saveAddress() {
        if (this.addressForm.valid) {
            // Set loading state
            this.savingAddress.set(true);

            // Get the complete form data for API call
            const agencyData = this.getAgencyFormData();

            this.agencyService.updateAgency(agencyData).subscribe({
                next: () => {
                    this.toast.success('Address updated successfully');
                    this.isEditingAddress.set(false);

                    // Refetch agency data to ensure UI has latest values
                    this.refreshAgencyData();
                },
                error: (err) => {
                    this.toast.error('Failed to update address');
                    console.error('Error updating address:', err);
                },
                complete: () => {
                    this.savingAddress.set(false);
                }
            });
        } else {
            this.toast.warning('Please fill in all required address fields');
        }
    }

    cancelAddressEdit() {
        const currentAgency = this.agency();
        this.addressForm.patchValue({
            firstLine: currentAgency?.addressFirstLine || '',
            secondLine: currentAgency?.addressSecondLine || '',
            town: currentAgency?.town || '',
            postcode: currentAgency?.postalCode || '',
            country: currentAgency?.country || '',
            county: currentAgency?.county || ''
        });
        this.isEditingAddress.set(false);
    }

    // Helper method to check if address has any data
    hasAddressData(): boolean {
        const agency = this.agency();
        return !!(agency?.addressFirstLine || agency?.town || agency?.postalCode || agency?.country);
    }

    // Helper method to format address for display
    getFormattedAddress(): string {
        const agency = this.agency();
        if (!agency) return 'Not specified';

        const parts: string[] = [];
        if (agency.addressFirstLine) parts.push(agency.addressFirstLine);
        if (agency.addressSecondLine) parts.push(agency.addressSecondLine);
        if (agency.town) parts.push(agency.town);
        if (agency.postalCode) parts.push(agency.postalCode);
        if (agency.country) parts.push(agency.country);

        return parts.length > 0 ? parts.join(', ') : 'Not specified';
    }

    // SMTP management methods
    toggleSMTPEdit() {
        if (!this.isSMTPConfigurationAllowed()) {
            this.toast.error('SMTP configuration is not available for this application');
            return;
        }
        this.isEditingSMTP.set(!this.isEditingSMTP());
    }

    saveSMTP() {
        if (!this.isSMTPConfigurationAllowed()) {
            this.toast.error('SMTP configuration is not available for this application');
            return;
        }

        if (this.smtpForm.valid) {
            const agencyId = this.agencyId();
            if (!agencyId) {
                this.toast.error('Agency ID not found');
                return;
            }

            // Set loading state
            this.savingSMTP.set(true);

            // Get SMTP form data and create email config object
            const smtpFormValue = this.smtpForm.value;
            const emailConfigData = {
                agencyId: agencyId,
                fromEmail: smtpFormValue.fromEmail,
                fromName: smtpFormValue.fromName,
                replyToEmail: smtpFormValue.replyToEmail,
                smtpHost: smtpFormValue.smtpHost,
                smtpPort: parseInt(smtpFormValue.smtpPort),
                smtpUsername: smtpFormValue.smtpUsername,
                smtpPassword: smtpFormValue.smtpPassword,
                smtpStarttlsEnable: smtpFormValue.smtpStarttlsEnable,
                smtpStarttlsRequired: smtpFormValue.smtpStarttlsRequired,
                supportEmail: smtpFormValue.supportEmail,
                websiteUrl: smtpFormValue.websiteUrl,
                notes: smtpFormValue.notes,
                id: this.smtpConfig()?.id || 0
            };

            this.agencyService.updateAgencyEmailConfig(agencyId, emailConfigData).subscribe({
                next: () => {
                    this.toast.success('SMTP settings updated successfully');
                    this.isEditingSMTP.set(false);

                    // Refetch email config to ensure UI has latest values
                    this.refreshAgencyData();
                },
                error: (err) => {
                    this.toast.error('Failed to update SMTP settings');
                    console.error('Error updating SMTP settings:', err);
                },
                complete: () => {
                    this.savingSMTP.set(false);
                }
            });
        } else {
            this.toast.warning('Please fill in all required SMTP fields correctly');
        }
    }

    cancelSMTPEdit() {
        if (!this.isSMTPConfigurationAllowed()) {
            this.toast.error('SMTP configuration is not available for this application');
            return;
        }

        const currentSMTPConfig = this.smtpConfig();
        if (currentSMTPConfig) {
            this.populateSMTPForm(currentSMTPConfig);
        } else {
            this.smtpForm.reset({
                fromEmail: '',
                fromName: '',
                replyToEmail: '',
                smtpHost: '',
                smtpPort: '',
                smtpUsername: '',
                smtpPassword: '',
                smtpStarttlsEnable: true,
                smtpStarttlsRequired: true,
                supportEmail: '',
                websiteUrl: '',
                notes: ''
            });
        }
        this.isEditingSMTP.set(false);
    }

    // Helper method to check if SMTP has any data
    hasSMTPData(): boolean {
        const smtpConfig = this.smtpConfig();
        return !!(smtpConfig?.smtpHost || smtpConfig?.smtpPort || smtpConfig?.smtpUsername);
    }

    // Helper method to format SMTP for display
    getFormattedSMTP(): string {
        const smtpConfig = this.smtpConfig();
        if (!smtpConfig || !this.hasSMTPData()) return 'Not configured';

        const parts: string[] = [];
        if (smtpConfig.smtpHost) parts.push(`Server: ${smtpConfig.smtpHost}`);
        if (smtpConfig.smtpPort) parts.push(`Port: ${smtpConfig.smtpPort}`);
        if (smtpConfig.smtpUsername) parts.push(`Username: ${smtpConfig.smtpUsername}`);

        return parts.length > 0 ? parts.join(', ') : 'Not configured';
    }

    // Helper method to check if SMTP settings should be available
    // SMTP configuration is only allowed for car rental applications that are not the main Karlink brand
    // This enables white-labeled car rental clients to configure their own SMTP settings
    isSMTPConfigurationAllowed(): boolean {
        return environment.isCarRental && environment.appName !== 'Karlink';
    }

    // Custom validator for URL fields - ensures URL starts with http:// or https://
    urlValidator(control: AbstractControl): ValidationErrors | null {
        if (!control.value || control.value.trim() === '') {
            return null; // Allow empty values (field is optional)
        }

        const urlValue = control.value.trim();
        const urlPattern = /^https?:\/\/.+/i;

        if (!urlPattern.test(urlValue)) {
            return { invalidUrl: { message: 'URL must start with http:// or https://' } };
        }

        return null;
    }

    // Method to get complete agency data for API submission
    getAgencyFormData() {
        const formValue = this.agencyForm.value;

        // Flatten the nested structure for API compatibility
        return {
            agencyId: this.agencyId(),
            name: formValue.companyProfile?.name,
            email: formValue.contactInfo?.email,
            telephone: formValue.contactInfo?.telephone,
            baseCurrency: formValue.companyProfile?.baseCurrency,
            billingEmail: formValue.contactInfo?.billingEmail,
            logo: formValue.companyProfile?.logo,
            serviceId: formValue.serviceId,
            address: {
                firstLine: formValue.address?.firstLine,
                secondLine: formValue.address?.secondLine,
                town: formValue.address?.town,
                postcode: formValue.address?.postcode,
                country: formValue.address?.country,
                county: formValue.address?.county
            }
        };
    }
}
