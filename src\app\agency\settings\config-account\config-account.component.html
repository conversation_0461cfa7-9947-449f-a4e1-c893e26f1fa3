<div class="container-fluid">
  <!-- Loading Spinner -->
  @if (loading()) {
  <div
    class="d-flex justify-content-center align-items-center"
    style="height: 200px"
  >
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  } @else if (error()) {
  <!-- Error State -->
  <div class="alert alert-danger" role="alert">
    <h5 class="alert-heading">
      <i class="fas fa-exclamation-triangle me-2"></i>Error Loading Agency Data
    </h5>
    <p class="mb-2">
      There was a problem loading your agency information. Please check your
      connection and try again.
    </p>
    <button class="btn btn-outline-danger btn-sm" (click)="refreshAgencyData()">
      <i class="fas fa-redo me-1"></i>Retry
    </button>
  </div>
  } @else {
  <!-- Main Content -->
  <div class="row">
    <div class="col-12">
      <!-- Company Profile Section -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0 text-dark font-weight-bold">Company Profile</h5>
          <small class="text-muted"
            >Manage your company branding and basic information</small
          >
        </div>
        <div class="card-body">
          <div class="row">
            <!-- Left Column: Logo Upload -->
            <div class="col-md-6 mb-4 mb-md-0">
              <h6 class="font-weight-semibold mb-3">Company Logo</h6>
              <div
                class="border border-dashed rounded p-4 text-center"
                style="border-color: #dee2e6"
              >
                <div class="mb-3">
                  <img
                    *ngIf="agency()?.logo; else placeholderLogo"
                    [src]="agency()?.logo"
                    alt="Company Logo"
                    class="img-thumbnail"
                    style="max-width: 150px; max-height: 100px"
                    onError="this.src='assets/images/Placeholder_500x500.jpg'"
                  />
                  <ng-template #placeholderLogo>
                    <div
                      class="bg-light rounded d-flex align-items-center justify-content-center"
                      style="width: 150px; height: 100px; margin: 0 auto"
                    >
                      <i class="fas fa-image text-muted fa-2x"></i>
                    </div>
                  </ng-template>
                </div>
                <div>
                  <p class="text-primary mb-2">
                    <i class="fas fa-upload mr-1"></i>
                    <strong>Upload a file or drag and drop</strong>
                  </p>
                  <p class="text-muted small mb-3">PNG, JPG up to 5MB</p>
                  <input
                    type="file"
                    id="logoInput"
                    class="form-control-file d-none"
                    (change)="onLogoChange($event)"
                    accept="image/png, image/jpg, image/jpeg"
                  />
                  <button
                    type="button"
                    class="btn btn-outline-primary btn-sm mr-2"
                    onclick="document.getElementById('logoInput').click()"
                  >
                    Choose File
                  </button>
                  <button
                    *ngIf="agencyLogo()"
                    type="button"
                    class="btn btn-primary btn-sm"
                    (click)="uploadLogo()"
                  >
                    Upload Logo
                  </button>
                </div>
              </div>
            </div>

            <!-- Right Column: Company Information -->
            <div class="col-md-6">
              <div class="form-group mb-4">
                <label for="companyName" class="font-weight-semibold"
                  >Company Name</label
                >
                <div
                  *ngIf="!isEditingCompanyName(); else editCompanyName"
                  class="d-flex align-items-center"
                >
                  <span class="form-control-plaintext">{{
                    agency()?.name || "Not specified"
                  }}</span>
                  <button
                    type="button"
                    class="btn btn-link btn-sm ml-2 p-0"
                    (click)="toggleCompanyNameEdit()"
                  >
                    <i class="fas fa-edit text-primary"></i>
                  </button>
                </div>
                <ng-template #editCompanyName>
                  <div class="input-group">
                    <input
                      type="text"
                      class="form-control"
                      [formControl]="companyProfileForm.get('name')"
                      placeholder="Enter company name"
                    />
                    <div class="input-group-append">
                      <button
                        type="button"
                        class="btn btn-success btn-sm"
                        (click)="saveCompanyName()"
                        [disabled]="
                          !companyProfileForm.get('name')?.valid ||
                          savingCompanyName()
                        "
                      >
                        <span
                          *ngIf="savingCompanyName()"
                          class="spinner-border mr-1"
                          role="status"
                          style="width: 0.8rem; height: 0.8rem"
                        ></span>
                        <i
                          *ngIf="!savingCompanyName()"
                          class="fas fa-check"
                        ></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-secondary btn-sm"
                        (click)="cancelCompanyNameEdit()"
                        [disabled]="savingCompanyName()"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </ng-template>
              </div>

              <div class="form-group">
                <label class="font-weight-semibold">Base Currency</label>
                <input
                  type="text"
                  class="form-control-plaintext bg-light"
                  [value]="agency()?.baseCurrency || 'USD'"
                  readonly
                />
                <small class="text-muted"
                  >Base currency is set during initial setup and cannot be
                  changed</small
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Information Section -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0 text-dark font-weight-bold">Contact Information</h5>
          <small class="text-muted">Manage your company contact details</small>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="font-weight-semibold">Email Address</label>
                <input
                  type="email"
                  class="form-control-plaintext bg-light"
                  [value]="agency()?.email || 'Not specified'"
                  readonly
                />
                <small class="text-muted"
                  >Email address is linked to your account and cannot be
                  changed</small
                >
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="font-weight-semibold">Phone Number</label>
                <div
                  *ngIf="!isEditingPhone(); else editPhone"
                  class="d-flex align-items-center"
                >
                  <span class="form-control-plaintext">{{
                    agency()?.telephone || "Not specified"
                  }}</span>
                  <button
                    type="button"
                    class="btn btn-link btn-sm ml-2 p-0"
                    (click)="togglePhoneEdit()"
                  >
                    <i class="fas fa-edit text-primary"></i>
                  </button>
                </div>
                <ng-template #editPhone>
                  <div class="d-flex align-items-start">
                    <div class="flex-grow-1 mr-2">
                      <phone-input
                        [formControl]="contactInfoForm.get('telephone')"
                        [defaultCountry]="'ZW'"
                        (validationStateChange)="
                          onPhoneValidationChange($event)
                        "
                      ></phone-input>
                      <div
                        *ngIf="
                          phoneValidationError() &&
                          contactInfoForm.get('telephone').touched
                        "
                        class="invalid-feedback d-block"
                      >
                        {{ phoneValidationError() }}
                      </div>
                    </div>
                    <div class="btn-group-vertical">
                      <button
                        type="button"
                        class="btn btn-success btn-sm mb-1"
                        (click)="savePhone()"
                        [disabled]="
                          !contactInfoForm.get('telephone').valid ||
                          phoneValidationError() ||
                          savingPhone()
                        "
                      >
                        <span
                          *ngIf="savingPhone()"
                          class="spinner-border mr-1"
                          role="status"
                          style="width: 0.8rem; height: 0.8rem"
                        ></span>
                        <i *ngIf="!savingPhone()" class="fas fa-check"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-secondary btn-sm"
                        (click)="cancelPhoneEdit()"
                        [disabled]="savingPhone()"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Address Details Section -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0 text-dark font-weight-bold">Address Details</h5>
          <small class="text-muted"
            >Manage your company address information</small
          >
        </div>
        <div class="card-body">
          <div *ngIf="!isEditingAddress(); else editAddress">
            <!-- Address Display Mode -->
            <div class="row">
              <div class="col-md-8">
                <div class="form-group">
                  <label class="font-weight-semibold">Company Address</label>
                  <div class="form-control-plaintext">
                    {{ getFormattedAddress() }}
                  </div>
                </div>
              </div>
              <div class="col-md-4 d-flex align-items-start">
                <button
                  type="button"
                  class="btn btn-outline-primary btn-sm mt-4"
                  (click)="toggleAddressEdit()"
                >
                  <i class="fas fa-edit mr-1"></i>
                  {{ hasAddressData() ? "Edit Address" : "Add Address" }}
                </button>
              </div>
            </div>
          </div>

          <!-- Address Edit Mode -->
          <ng-template #editAddress>
            <form [formGroup]="addressForm">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="firstLine" class="font-weight-semibold"
                      >Street Address <span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      id="firstLine"
                      class="form-control"
                      formControlName="firstLine"
                      placeholder="1234 Main St"
                      [class.is-invalid]="
                        addressForm.get('firstLine').invalid &&
                        addressForm.get('firstLine').touched
                      "
                    />
                    <div
                      *ngIf="
                        addressForm.get('firstLine').invalid &&
                        addressForm.get('firstLine').touched
                      "
                      class="invalid-feedback"
                    >
                      Street address is required
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="secondLine" class="font-weight-semibold"
                      >Address Line 2</label
                    >
                    <input
                      type="text"
                      id="secondLine"
                      class="form-control"
                      formControlName="secondLine"
                      placeholder="Apartment, studio, or floor"
                    />
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="town" class="font-weight-semibold"
                      >City / Town <span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      id="town"
                      class="form-control"
                      formControlName="town"
                      placeholder="Enter city or town"
                      [class.is-invalid]="
                        addressForm.get('town').invalid &&
                        addressForm.get('town').touched
                      "
                    />
                    <div
                      *ngIf="
                        addressForm.get('town').invalid &&
                        addressForm.get('town').touched
                      "
                      class="invalid-feedback"
                    >
                      City/Town is required
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="postalCode" class="font-weight-semibold"
                      >ZIP / Postal Code</label
                    >
                    <input
                      type="text"
                      id="postalCode"
                      class="form-control"
                      formControlName="postcode"
                      placeholder="Enter postal code (optional)"
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="country" class="font-weight-semibold"
                      >Country <span class="text-danger">*</span></label
                    >
                    <country-selector formControlName="country">
                    </country-selector>
                    <div
                      *ngIf="
                        addressForm.get('country').invalid &&
                        addressForm.get('country').touched
                      "
                      class="invalid-feedback"
                    >
                      Country is required
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <div class="d-flex">
                    <button
                      type="button"
                      class="btn btn-success mr-2"
                      (click)="saveAddress()"
                      [disabled]="savingAddress()"
                    >
                      <span
                        *ngIf="savingAddress()"
                        class="spinner-border mr-1"
                        role="status"
                        style="width: 0.8rem; height: 0.8rem"
                      ></span>
                      <i *ngIf="!savingAddress()" class="fas fa-check mr-1"></i>
                      Save Address
                    </button>
                    <button
                      type="button"
                      class="btn btn-secondary"
                      (click)="cancelAddressEdit()"
                      [disabled]="savingAddress()"
                    >
                      <i class="fas fa-times mr-1"></i>
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </ng-template>
        </div>
      </div>

      <!-- SMTP Configuration Section -->
      <!-- <div class="card shadow-sm mb-4" *ngIf="isSMTPConfigurationAllowed()">
        <div class="card-header bg-white">
          <h5 class="mb-0 text-dark font-weight-bold">SMTP Configuration</h5>
          <small class="text-muted"
            >Configure email server settings for notifications</small
          >
        </div>
        <div class="card-body">
          <div *ngIf="!isEditingSMTP(); else editSMTP">
            <div class="row">
              <div class="col-md-8">
                <div class="form-group">
                  <label class="font-weight-semibold"
                    >Email Server Settings</label
                  >
                  <div class="form-control-plaintext">
                    {{ getFormattedSMTP() }}
                  </div>
                </div>
              </div>
              <div class="col-md-4 d-flex align-items-start">
                <button
                  type="button"
                  class="btn btn-outline-primary btn-sm mt-4"
                  (click)="toggleSMTPEdit()"
                >
                  <i class="fas fa-edit mr-1"></i>
                  {{ hasSMTPData() ? "Edit SMTP" : "Configure SMTP" }}
                </button>
              </div>
            </div>
          </div>

          <ng-template #editSMTP>
            <form [formGroup]="smtpForm">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="smtpHost" class="font-weight-semibold"
                      >SMTP Server <span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      id="smtpHost"
                      class="form-control"
                      placeholder="e.g., smtp.gmail.com"
                      formControlName="smtpHost"
                      [class.is-invalid]="
                        smtpForm.get('smtpHost').invalid &&
                        smtpForm.get('smtpHost').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('smtpHost').invalid &&
                        smtpForm.get('smtpHost').touched
                      "
                      class="invalid-feedback"
                    >
                      SMTP server is required
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="smtpPort" class="font-weight-semibold"
                      >Port <span class="text-danger">*</span></label
                    >
                    <input
                      type="number"
                      id="smtpPort"
                      class="form-control"
                      placeholder="e.g., 587"
                      formControlName="smtpPort"
                      [class.is-invalid]="
                        smtpForm.get('smtpPort').invalid &&
                        smtpForm.get('smtpPort').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('smtpPort').invalid &&
                        smtpForm.get('smtpPort').touched
                      "
                      class="invalid-feedback"
                    >
                      <span
                        *ngIf="smtpForm.get('smtpPort').errors?.['required']"
                        >Port is required</span
                      >
                      <span *ngIf="smtpForm.get('smtpPort').errors?.['pattern']"
                        >Port must be a number</span
                      >
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="smtpUsername" class="font-weight-semibold"
                      >Username (Email)
                      <span class="text-danger">*</span></label
                    >
                    <input
                      type="email"
                      id="smtpUsername"
                      class="form-control"
                      placeholder="<EMAIL>"
                      formControlName="smtpUsername"
                      [class.is-invalid]="
                        smtpForm.get('smtpUsername').invalid &&
                        smtpForm.get('smtpUsername').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('smtpUsername').invalid &&
                        smtpForm.get('smtpUsername').touched
                      "
                      class="invalid-feedback"
                    >
                      <span
                        *ngIf="smtpForm.get('smtpUsername').errors?.['required']"
                        >Username is required</span
                      >
                      <span
                        *ngIf="smtpForm.get('smtpUsername').errors?.['email']"
                        >Please enter a valid email</span
                      >
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="smtpPassword" class="font-weight-semibold"
                      >Password <span class="text-danger">*</span></label
                    >
                    <input
                      type="password"
                      id="smtpPassword"
                      class="form-control"
                      placeholder="Enter password"
                      formControlName="smtpPassword"
                      [class.is-invalid]="
                        smtpForm.get('smtpPassword').invalid &&
                        smtpForm.get('smtpPassword').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('smtpPassword').invalid &&
                        smtpForm.get('smtpPassword').touched
                      "
                      class="invalid-feedback"
                    >
                      Password is required
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="fromEmail" class="font-weight-semibold"
                      >From Email <span class="text-danger">*</span></label
                    >
                    <input
                      type="email"
                      id="fromEmail"
                      class="form-control"
                      placeholder="<EMAIL>"
                      formControlName="fromEmail"
                      [class.is-invalid]="
                        smtpForm.get('fromEmail').invalid &&
                        smtpForm.get('fromEmail').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('fromEmail').invalid &&
                        smtpForm.get('fromEmail').touched
                      "
                      class="invalid-feedback"
                    >
                      <span
                        *ngIf="smtpForm.get('fromEmail').errors?.['required']"
                        >From email is required</span
                      >
                      <span *ngIf="smtpForm.get('fromEmail').errors?.['email']"
                        >Please enter a valid email</span
                      >
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="fromName" class="font-weight-semibold"
                      >From Name <span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      id="fromName"
                      class="form-control"
                      placeholder="Your Company Name"
                      formControlName="fromName"
                      [class.is-invalid]="
                        smtpForm.get('fromName').invalid &&
                        smtpForm.get('fromName').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('fromName').invalid &&
                        smtpForm.get('fromName').touched
                      "
                      class="invalid-feedback"
                    >
                      From name is required
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="replyToEmail" class="font-weight-semibold"
                      >Reply-To Email</label
                    >
                    <input
                      type="email"
                      id="replyToEmail"
                      class="form-control"
                      placeholder="<EMAIL>"
                      formControlName="replyToEmail"
                      [class.is-invalid]="
                        smtpForm.get('replyToEmail').invalid &&
                        smtpForm.get('replyToEmail').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('replyToEmail').invalid &&
                        smtpForm.get('replyToEmail').touched
                      "
                      class="invalid-feedback"
                    >
                      Please enter a valid email
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="supportEmail" class="font-weight-semibold"
                      >Support Email</label
                    >
                    <input
                      type="email"
                      id="supportEmail"
                      class="form-control"
                      placeholder="<EMAIL>"
                      formControlName="supportEmail"
                      [class.is-invalid]="
                        smtpForm.get('supportEmail').invalid &&
                        smtpForm.get('supportEmail').touched
                      "
                    />
                    <div
                      *ngIf="
                        smtpForm.get('supportEmail').invalid &&
                        smtpForm.get('supportEmail').touched
                      "
                      class="invalid-feedback"
                    >
                      Please enter a valid email
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="websiteUrl" class="font-weight-semibold"
                      >Website URL</label
                    >
                    <input
                      type="url"
                      id="websiteUrl"
                      class="form-control"
                      placeholder="https://yourcompany.com"
                      formControlName="websiteUrl"
                      [class.is-invalid]="
                        smtpForm.get('websiteUrl')?.invalid &&
                        smtpForm.get('websiteUrl')?.touched
                      "
                    />
                    <div
                      class="invalid-feedback"
                      *ngIf="
                        smtpForm.get('websiteUrl')?.invalid &&
                        smtpForm.get('websiteUrl')?.touched
                      "
                    >
                      <div
                        *ngIf="smtpForm.get('websiteUrl')?.errors?.['invalidUrl']"
                      >
                        {{ smtpForm.get('websiteUrl')?.errors?.['invalidUrl']?.message }}
                      </div>
                    </div>
                    <small class="form-text text-muted"
                      >Your company website URL</small
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label class="font-weight-semibold"
                      >Security Settings</label
                    >
                    <div class="form-check">
                      <input
                        type="checkbox"
                        id="smtpStarttlsEnable"
                        class="form-check-input"
                        formControlName="smtpStarttlsEnable"
                      />
                      <label for="smtpStarttlsEnable" class="form-check-label">
                        Enable STARTTLS
                      </label>
                    </div>
                    <small class="form-text text-muted"
                      >Enable STARTTLS for secure email transmission</small
                    >
                    <div class="form-check mt-2">
                      <input
                        type="checkbox"
                        id="smtpStarttlsRequired"
                        class="form-check-input"
                        formControlName="smtpStarttlsRequired"
                      />
                      <label
                        for="smtpStarttlsRequired"
                        class="form-check-label"
                      >
                        Require STARTTLS
                      </label>
                    </div>
                    <small class="form-text text-muted"
                      >Require STARTTLS connection (recommended)</small
                    >
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <div class="d-flex">
                    <button
                      type="button"
                      class="btn btn-success mr-2"
                      (click)="saveSMTP()"
                      [disabled]="smtpForm.invalid || savingSMTP()"
                    >
                      <span
                        *ngIf="savingSMTP()"
                        class="spinner-border mr-1"
                        role="status"
                        style="width: 0.8rem; height: 0.8rem"
                      ></span>
                      <i *ngIf="!savingSMTP()" class="fas fa-check mr-1"></i>
                      Save SMTP Settings
                    </button>
                    <button
                      type="button"
                      class="btn btn-secondary"
                      (click)="cancelSMTPEdit()"
                      [disabled]="savingSMTP()"
                    >
                      <i class="fas fa-times mr-1"></i>
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </ng-template>
        </div>
      </div> -->
    </div>
  </div>
  }
</div>
